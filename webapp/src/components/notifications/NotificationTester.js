import React from 'react';
import { useNotifications } from '../../context/NotificationContext';

/**
 * NotificationTester Component
 * 
 * This component is for testing the order notification system.
 * It provides buttons to simulate different types of order notifications.
 * 
 * Usage: Add this component to any page during development to test notifications.
 * Remove before production.
 */
const NotificationTester = () => {
    const { addOrderNotification, addNotification } = useNotifications();

    const testOrderNotifications = [
        {
            type: 'order_placed',
            title: 'New Order #12345',
            body: 'You have a new order from <PERSON>',
            data: {
                type: 'order_placed',
                order_id: '12345',
                customer_name: '<PERSON>',
                service_name: 'Gaming Session',
                amount: '50.00',
                is_scheduled: false,
                expires_at: new Date(Date.now() + 30000).toISOString() // 30 seconds from now
            }
        },
        {
            type: 'order_accepted',
            title: 'Order #12346 Accepted',
            body: 'Your order has been accepted by <PERSON>',
            data: {
                type: 'order_accepted',
                order_id: '12346',
                talent_name: '<PERSON>',
                service_name: 'Coaching Session',
                amount: '75.00',
                is_scheduled: true
            }
        },
        {
            type: 'order_rejected',
            title: 'Order #12347 Rejected',
            body: 'Your order has been rejected by <PERSON>',
            data: {
                type: 'order_rejected',
                order_id: '12347',
                talent_name: '<PERSON> <PERSON>',
                service_name: 'Tournament Play',
                amount: '100.00',
                is_scheduled: false
            }
        },
        {
            type: 'order_completed',
            title: 'Order #12348 Completed',
            body: 'Your order has been completed successfully',
            data: {
                type: 'order_completed',
                order_id: '12348',
                talent_name: 'Sarah Wilson',
                service_name: 'Skill Training',
                amount: '60.00',
                is_scheduled: false
            }
        },
        {
            type: 'order_cancelled',
            title: 'Order #12349 Cancelled',
            body: 'Your order has been cancelled',
            data: {
                type: 'order_cancelled',
                order_id: '12349',
                customer_name: 'Alex Brown',
                service_name: 'Practice Match',
                amount: '40.00',
                is_scheduled: true
            }
        }
    ];

    const handleTestNotification = (notification) => {
        addOrderNotification({
            title: notification.title,
            body: notification.body,
            data: notification.data
        });
    };

    const handleTestGenericNotification = (type) => {
        const messages = {
            success: 'This is a success notification!',
            error: 'This is an error notification!',
            warning: 'This is a warning notification!',
            info: 'This is an info notification!'
        };

        addNotification(messages[type], type, 5000);
    };

    if (process.env.NODE_ENV !== 'development') {
        return null; // Don't show in production
    }

    return (
        <div className="fixed bottom-4 left-4 z-50 bg-white rounded-lg shadow-lg border border-gray-200 p-4 max-w-sm">
            <h3 className="text-sm font-bold text-gray-800 mb-3">🧪 Notification Tester</h3>
            
            <div className="space-y-2">
                <div>
                    <h4 className="text-xs font-semibold text-gray-600 mb-2">Order Notifications:</h4>
                    <div className="grid grid-cols-2 gap-1">
                        {testOrderNotifications.map((notification, index) => (
                            <button
                                key={index}
                                onClick={() => handleTestNotification(notification)}
                                className="text-xs px-2 py-1 bg-blue-100 hover:bg-blue-200 text-blue-800 rounded transition-colors"
                            >
                                {notification.data.type.replace('order_', '').replace('_', ' ')}
                            </button>
                        ))}
                    </div>
                </div>

                <div>
                    <h4 className="text-xs font-semibold text-gray-600 mb-2">Generic Notifications:</h4>
                    <div className="grid grid-cols-2 gap-1">
                        {['success', 'error', 'warning', 'info'].map((type) => (
                            <button
                                key={type}
                                onClick={() => handleTestGenericNotification(type)}
                                className={`text-xs px-2 py-1 rounded transition-colors ${
                                    type === 'success' ? 'bg-green-100 hover:bg-green-200 text-green-800' :
                                    type === 'error' ? 'bg-red-100 hover:bg-red-200 text-red-800' :
                                    type === 'warning' ? 'bg-yellow-100 hover:bg-yellow-200 text-yellow-800' :
                                    'bg-blue-100 hover:bg-blue-200 text-blue-800'
                                }`}
                            >
                                {type}
                            </button>
                        ))}
                    </div>
                </div>
            </div>

            <div className="mt-3 pt-2 border-t border-gray-200">
                <p className="text-xs text-gray-500">
                    Development only - tests notification system
                </p>
            </div>
        </div>
    );
};

export default NotificationTester;
